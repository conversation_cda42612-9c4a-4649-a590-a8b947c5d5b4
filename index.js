export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    
    // 检查请求路径是否为 /v1/chat/completions
    if (url.pathname === '/v1/chat/completions') {
      // 创建目标URL
      const targetUrl = 'https://shadow.wandakeeling36.workers.dev/p3yRs6wX/https://kilocode.ai/api/openrouter/chat/completions';
      
      let modifiedBody = request.body;
      let modifiedHeaders = new Headers(request.headers);
      let bodyJson = null;
      
      // 如果是POST请求且有body，检查并替换模型名称
      if (request.method === 'POST' && request.body) {
        try {
          const bodyText = await request.text();
          bodyJson = JSON.parse(bodyText);
          
          // 模型重定向配置
          const modelMapping = {
            'claude-sonnet-4-20250514': 'anthropic/claude-sonnet-4',
            'claude-sonnet-4-20250514-thinking': 'anthropic/claude-sonnet-4',
            'claude-opus-4-20250514': 'anthropic/claude-4-opus-20250522',
            'claude-opus-4-20250514-thinking': 'anthropic/claude-4-opus-20250522',
            'grok-4': 'x-ai/grok-4',
            'o3-pro':'openai/o3-pro',
            'o4-mini':'openai/o4-mini',
            'o3':'openai/o3',
            'o3-mini':'openai/o3-mini',
            'gpt-4.1':'openai/gpt-4.1'
            // 可以在这里轻松添加更多模型映射
          };
          
          // 检查是否需要重定向
          if (modelMapping[bodyJson.model]) {
            const isThinkingModel = bodyJson.model.endsWith('-thinking');
            const originalModel = bodyJson.model; // 保存原始模型名称
            bodyJson.model = modelMapping[bodyJson.model];
            
            // thinking模型的特殊配置
            if (isThinkingModel) {
              // 为不同的thinking模型设置不同的配置
              if (originalModel === 'claude-sonnet-4-20250514-thinking') {
                // claude-sonnet-4-thinking 使用 64000
                bodyJson.max_tokens = 64000;
                bodyJson.reasoning = {
                  "max_tokens": 63999
                };
              } else if (originalModel === 'claude-opus-4-20250514-thinking') {
                // claude-opus-4-thinking 使用 32000
                bodyJson.max_tokens = 32000;
                bodyJson.reasoning = {
                  "max_tokens": 31999
                };
              } else {
                // 其他thinking模型的默认配置
                bodyJson.max_tokens = 64000;
                bodyJson.reasoning = {
                  "max_tokens": 63999
                };
              }
            }
          }
          
          // 为Claude模型自动启用缓存
          if (bodyJson.model && bodyJson.model.includes('anthropic/claude')) {
            this.applyCacheControl(bodyJson);
          }
          
          const modifiedBodyText = JSON.stringify(bodyJson);
          modifiedBody = modifiedBodyText;
          
          // 更新Content-Length头部
          modifiedHeaders.set('Content-Length', new TextEncoder().encode(modifiedBodyText).length.toString());
        } catch (error) {
          // 如果解析JSON失败，保持原始body
          console.error('Failed to parse request body:', error);
        }
      }
      
      // 创建新的请求，使用修改后的body和headers
      const modifiedRequest = new Request(targetUrl, {
        method: request.method,
        headers: modifiedHeaders,
        body: modifiedBody,
        redirect: 'follow'
      });
      
      try {
        // 转发请求到目标服务器
        const response = await fetch(modifiedRequest);
        
        // 检查是否为流式响应
        if (bodyJson && bodyJson.stream) {
          // 处理流式响应
          return this.handleStreamResponse(response);
        } else {
          // 处理非流式响应
          return this.handleNonStreamResponse(response);
        }
      } catch (error) {
        // 如果转发失败，返回错误响应
        return new Response(JSON.stringify({
          error: 'Internal Server Error',
          message: 'Failed to forward request'
        }), {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }
    }
    
    // 对于其他路径，返回404
    return new Response(JSON.stringify({
      error: 'Not Found',
      message: 'Endpoint not found'
    }), {
      status: 404,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },

  // 处理流式响应
  async handleStreamResponse(response) {
    const { readable, writable } = new TransformStream();
    const writer = writable.getWriter();
    const decoder = new TextDecoder();
    let buffer = '';

    // 异步处理流
    (async () => {
      try {
        const reader = response.body.getReader();

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });

          // 按行处理 SSE 事件
          let eolIndex;
          while ((eolIndex = buffer.indexOf('\n')) >= 0) {
            const line = buffer.substring(0, eolIndex).trim();
            buffer = buffer.substring(eolIndex + 1);

            if (line.startsWith('data: ')) {
              const jsonData = line.substring('data: '.length);
              if (jsonData === '[DONE]') {
                await writer.write(new TextEncoder().encode(`data: ${jsonData}\n\n`));
              } else {
                try {
                  const parsedChunk = JSON.parse(jsonData);
                  // 转换 reasoning 字段
                  this.transformReasoningFields(parsedChunk);
                  await writer.write(new TextEncoder().encode(`data: ${JSON.stringify(parsedChunk)}\n\n`));
                } catch (parseError) {
                  console.error('Error parsing JSON chunk:', parseError, 'Original data:', jsonData);
                  // 如果解析失败，转发原始数据
                  await writer.write(new TextEncoder().encode(`${line}\n\n`));
                }
              }
            } else if (line) {
              // 转发其他非 data 行 (例如 event: completion)
              await writer.write(new TextEncoder().encode(`${line}\n\n`));
            }
          }
        }

        // 处理可能残留在 buffer 中的数据
        if (buffer.trim().startsWith('data: ')) {
          const jsonData = buffer.trim().substring('data: '.length);
          if (jsonData === '[DONE]') {
            await writer.write(new TextEncoder().encode(`data: ${jsonData}\n\n`));
          } else {
            try {
              const chunk = JSON.parse(jsonData);
              this.transformReasoningFields(chunk);
              await writer.write(new TextEncoder().encode(`data: ${JSON.stringify(chunk)}\n\n`));
            } catch (e) {
              console.error('Error parsing JSON chunk from remaining buffer:', e, 'Original data:', jsonData);
            }
          }
        }

        await writer.close();
      } catch (error) {
        console.error('Error processing stream:', error);
        await writer.abort(error);
      }
    })();

    // 检查状态码，如果是402则替换为401
    const finalStatus = response.status === 402 ? 401 : response.status;
    const finalStatusText = response.status === 402 ? 'Unauthorized' : response.statusText;

    return new Response(readable, {
      status: finalStatus,
      statusText: finalStatusText,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': '*'
      }
    });
  },

  // 处理非流式响应
  async handleNonStreamResponse(response) {
    try {
      const responseData = await response.json();
      
      // 转换 reasoning 字段
      this.transformReasoningFields(responseData);
      
      return new Response(JSON.stringify(responseData), {
        status: response.status,
        statusText: response.statusText,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': '*'
        }
      });
    } catch (error) {
      console.error('Error processing non-stream response:', error);
      return new Response(JSON.stringify({
        error: 'Failed to process response',
        message: 'Error parsing response from upstream'
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  },

  // 转换 reasoning 字段为 reasoning_content
  transformReasoningFields(data) {
    if (data.choices && data.choices.length > 0) {
      data.choices.forEach((choice) => {
        // 处理 delta 中的 reasoning 字段（流式响应）
        if (choice.delta && choice.delta.hasOwnProperty('reasoning')) {
          choice.delta.reasoning_content = choice.delta.reasoning;
          delete choice.delta.reasoning;
        }
        // 处理 message 中的 reasoning 字段（非流式响应）
        if (choice.message && choice.message.hasOwnProperty('reasoning')) {
          choice.message.reasoning_content = choice.message.reasoning;
          delete choice.message.reasoning;
        }
      });
    }
  },

  // 智能应用缓存控制，确保不超过4个缓存块限制
  applyCacheControl(bodyJson) {
    if (!bodyJson.messages || !Array.isArray(bodyJson.messages)) {
      return;
    }

    // 彻底清除所有现有的缓存控制
    this.clearAllCacheControl(bodyJson);
    
    // 验证清除后的状态
    let initialCount = this.countCacheBlocks(bodyJson);
    console.log(`清除缓存后的数量: ${initialCount}`);

    // 收集所有可缓存的内容块
    const cacheableBlocks = [];

    bodyJson.messages.forEach((message, messageIndex) => {
      if (!message.content) return;

      // 处理字符串格式的content
      if (typeof message.content === 'string') {
        if (message.content.length > 1000) {
          cacheableBlocks.push({
            messageIndex,
            contentIndex: -1, // -1表示整个content是字符串
            length: message.content.length
          });
        }
      }
      // 处理数组格式的content
      else if (Array.isArray(message.content)) {
        message.content.forEach((contentBlock, contentIndex) => {
          if (contentBlock.type === 'text' && contentBlock.text && contentBlock.text.length > 1000) {
            cacheableBlocks.push({
              messageIndex,
              contentIndex,
              length: contentBlock.text.length
            });
          }
        });
      }
    });

    // 按文本长度排序，优先缓存最长的内容
    cacheableBlocks.sort((a, b) => b.length - a.length);

    // 严格限制：最多只添加3个缓存控制块（保险起见）
    const maxCacheBlocks = Math.min(3, cacheableBlocks.length);
    console.log(`准备添加 ${maxCacheBlocks} 个缓存控制块`);
    
    for (let i = 0; i < maxCacheBlocks; i++) {
      const block = cacheableBlocks[i];
      const message = bodyJson.messages[block.messageIndex];

      if (block.contentIndex === -1) {
        // 字符串格式，转换为数组格式并添加缓存
        message.content = [
          {
            "type": "text",
            "text": message.content,
            "cache_control": {
              "type": "ephemeral"
            }
          }
        ];
      } else {
        // 数组格式，直接为指定块添加缓存
        const contentBlock = message.content[block.contentIndex];
        contentBlock.cache_control = {
          "type": "ephemeral"
        };
      }
    }

    // 最终验证
    const finalCount = this.countCacheBlocks(bodyJson);
    console.log(`最终缓存控制块数量: ${finalCount}`);
    
    if (finalCount > 4) {
      console.error(`警告：缓存控制块数量超限 (${finalCount}), 强制清除所有缓存`);
      this.clearAllCacheControl(bodyJson);
    }
  },

  // 彻底清除所有缓存控制
  clearAllCacheControl(bodyJson) {
    if (!bodyJson.messages || !Array.isArray(bodyJson.messages)) {
      return;
    }

    bodyJson.messages.forEach(message => {
      if (!message.content) return;

      // 处理数组格式的content
      if (Array.isArray(message.content)) {
        message.content.forEach(contentBlock => {
          if (contentBlock && contentBlock.cache_control) {
            delete contentBlock.cache_control;
          }
        });
      }
      // 如果content是字符串且之前被转换过，需要重置
      // 这里不处理字符串，因为字符串不会有cache_control
    });
  },

  // 计算当前缓存控制块数量
  countCacheBlocks(bodyJson) {
    if (!bodyJson.messages || !Array.isArray(bodyJson.messages)) {
      return 0;
    }

    let count = 0;
    bodyJson.messages.forEach(message => {
      if (!message.content) return;

      if (Array.isArray(message.content)) {
        message.content.forEach(contentBlock => {
          if (contentBlock && contentBlock.cache_control) {
            count++;
          }
        });
      }
    });

    return count;
  }
};
